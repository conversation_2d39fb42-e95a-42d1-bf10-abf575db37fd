'use client';

import { useTranslations } from 'next-intl';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Heart, Share, Play, Music, Users } from 'lucide-react';

export default function ArtistPage() {
  const t = useTranslations('ArtistPage');

  // Simple mock data for demo
  const mockArtist = {
    name: 'The Weeknd',
    followers: 45000000,
    monthlyListeners: 85000000,
    genres: ['R&B', 'Pop', 'Alternative R&B'],
    verified: true,
    imageUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop&crop=face'
  };

  const mockTracks = [
    { id: '1', title: 'Blinding Lights', duration: '3:20', plays: '2.8B' },
    { id: '2', title: 'The Hills', duration: '4:02', plays: '1.9B' },
    { id: '3', title: 'Starboy', duration: '3:50', plays: '1.7B' },
    { id: '4', title: 'Can\'t Feel My Face', duration: '3:33', plays: '1.5B' },
    { id: '5', title: 'Earned It', duration: '4:18', plays: '1.2B' }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Artist Header */}
      <div className="bg-gradient-to-b from-primary/10 to-background p-6">
        <div className="max-w-4xl mx-auto flex flex-col md:flex-row gap-6">
          <Avatar className="w-48 h-48 mx-auto md:mx-0">
            <AvatarImage src={mockArtist.imageUrl} alt={mockArtist.name} />
            <AvatarFallback className="text-4xl">{mockArtist.name[0]}</AvatarFallback>
          </Avatar>

          <div className="flex-1 text-center md:text-left space-y-4">
            <div>
              <h1 className="text-4xl font-bold">{mockArtist.name}</h1>
              <div className="flex flex-wrap gap-2 justify-center md:justify-start mt-2">
                {mockArtist.genres.map((genre) => (
                  <Badge key={genre} variant="secondary">{genre}</Badge>
                ))}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span className="font-semibold text-foreground">45M</span> {t('followers')}
              </div>
              <div className="flex items-center gap-1">
                <Music className="w-4 h-4" />
                <span className="font-semibold text-foreground">85M</span> {t('monthlyListeners')}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
              <Button className="gap-2">
                <Heart className="w-4 h-4" />
                {t('follow')}
              </Button>
              <Button variant="outline" className="gap-2">
                <Share className="w-4 h-4" />
                {t('share')}
              </Button>
              <Button variant="default" className="gap-2 bg-green-600 hover:bg-green-700">
                <Play className="w-4 h-4" />
                {t('play')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Tracks */}
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="w-5 h-5" />
              {t('popularTracks')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {mockTracks.map((track, index) => (
              <div key={track.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-accent cursor-pointer">
                <span className="text-sm text-muted-foreground w-6">{index + 1}</span>
                <div className="flex-1">
                  <h3 className="font-medium">{track.title}</h3>
                </div>
                <span className="text-sm text-muted-foreground">{track.plays}</span>
                <span className="text-sm text-muted-foreground font-mono">{track.duration}</span>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
